# Notion Page Integration Example

## Overview
The items-generator can now extract items directly from Notion pages! Here's how to use the Notion page you provided.

## API Request Example

### Option 1: URL in Prompt (Recommended)
```json
POST /items-generator
{
  "slug": "pm-time-tracking-tools",
  "name": "PM and Time Tracking Software",
  "prompt": "Extract PM and time tracking software from https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278",
  "initial_categories": ["Project Management", "Time Tracking", "Productivity", "Software"],
  "config": {
    "max_pages_to_process": 50,
    "relevance_threshold_content": 0.7,
    "ai_first_generation_enabled": true
  }
}
```

### Option 2: URL in source_urls field
```json
POST /items-generator
{
  "slug": "pm-time-tracking-tools", 
  "name": "PM and Time Tracking Software",
  "prompt": "Find and categorize project management and time tracking software tools",
  "source_urls": ["https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278"],
  "initial_categories": ["Project Management", "Time Tracking", "Productivity"]
}
```

## How It Works

### 1. URL Extraction & Processing
- The `PromptProcessingService` extracts the Notion URL from your prompt
- It also extracts category hints like "PM", "time tracking", "software"
- The prompt is rewritten to focus on the content extraction task

### 2. Content Retrieval
- The `WebPageRetrievalService` uses Tavily API to extract content from the Notion page
- Tavily is excellent at handling dynamic content like Notion pages
- The raw content is retrieved and stored for processing

### 3. AI Item Extraction
- The `ItemExtractionService` uses AI to parse the Notion page content
- It identifies individual tools/software mentioned in the page
- For each tool, it extracts:
  - Name
  - Description
  - Source URL (if available)
  - Relevant features

### 4. Categorization
- Items are categorized using your `initial_categories`
- The AI prioritizes the categories you specified
- Additional categories may be created if needed

### 5. Expected Output
```json
{
  "items": [
    {
      "name": "Asana",
      "description": "Project management tool for team collaboration and task tracking",
      "source_url": "https://asana.com",
      "category": "project-management",
      "tags": ["collaboration", "task-management", "team-productivity"],
      "slug": "asana"
    },
    {
      "name": "Toggl Track", 
      "description": "Time tracking software for individuals and teams",
      "source_url": "https://toggl.com/track",
      "category": "time-tracking",
      "tags": ["time-tracking", "productivity", "reporting"],
      "slug": "toggl-track"
    }
    // ... more items extracted from the Notion page
  ],
  "categories": [
    { "id": "project-management", "name": "Project Management" },
    { "id": "time-tracking", "name": "Time Tracking" },
    { "id": "productivity", "name": "Productivity" }
  ],
  "tags": [
    { "id": "collaboration", "name": "Collaboration" },
    { "id": "task-management", "name": "Task Management" },
    // ... more tags
  ]
}
```

## Benefits

### ✅ What Works Great
- **Dynamic Content**: Tavily handles Notion's dynamic content loading
- **Structured Extraction**: AI parses unstructured content into structured items
- **Category Consistency**: Your initial categories ensure proper organization
- **Automatic Enhancement**: AI can find additional details and source URLs for items
- **Deduplication**: Prevents duplicate items if the same tool appears multiple times

### 🔧 Configuration Tips
- Set `max_pages_to_process` higher if you have many URLs
- Adjust `relevance_threshold_content` (0.7-0.8 recommended for Notion pages)
- Use `ai_first_generation_enabled: true` for better results
- Provide specific `initial_categories` for better categorization

### 🚀 Advanced Usage
You can combine the Notion page with additional search:
```json
{
  "prompt": "Extract tools from https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278 and find additional project management software",
  "target_keywords": ["project management", "time tracking", "productivity tools"],
  "initial_categories": ["Project Management", "Time Tracking"]
}
```

This will:
1. Extract items from your Notion page
2. Search the web for additional tools
3. Combine and deduplicate all findings
4. Categorize everything consistently

## Testing
To test this functionality:
1. Ensure `TAVILY_API_KEY` is configured in your `.env` file
2. Use the API request examples above
3. Check the logs for extraction progress
4. Review the generated items and categories

The system should successfully extract all the PM and time tracking tools from your Notion page! 🎉
