// @ts-check

/**
 * Test script to verify Notion page content extraction
 * This tests if the items-generator can extract content from the Notion page
 */

const { tavily } = require('@tavily/core');
const axios = require('axios');
const cheerio = require('cheerio');

// Try to import Notion packages if available
const notionClient = require('@notionhq/client');
const Client = notionClient.Client;
const notionToMd = require('notion-to-md');
const NotionToMarkdown = notionToMd.NotionToMarkdown;

require('dotenv').config();

// Real working Notion extraction implementation
async function testRealNotionExtraction(notionUrl) {
    console.log('\n🔧 Testing REAL Notion extraction implementation...');

    // Extract page ID from Notion URL
    const pageId = extractNotionPageId(notionUrl);
    if (!pageId) {
        console.log('❌ Could not extract page ID from URL');
        return null;
    }

    console.log(`📄 Extracted page ID: ${pageId}`);

    // Try different approaches for public pages
    const approaches = [
        () => extractWithNotionAPI(pageId),
        () => extractWithUnofficial(notionUrl, pageId),
        () => extractWithPublicAPI(pageId),
    ];

    for (let i = 0; i < approaches.length; i++) {
        try {
            console.log(`🔄 Trying approach ${i + 1}...`);
            const result = await approaches[i]();
            if (result) {
                console.log('✅ Notion extraction successful!');
                console.log(`📊 Content length: ${result.length} characters`);

                // Show sample content
                const lines = result.split('\n').filter(line => line.trim().length > 0).slice(0, 10);
                if (lines.length > 0) {
                    console.log('\n📝 Sample extracted content:');
                    lines.forEach((line, index) => {
                        console.log(`${index + 1}. ${line.trim()}`);
                    });
                }

                return result;
            }
        } catch (error) {
            console.log(`❌ Approach ${i + 1} failed: ${error.message}`);
        }
    }

    console.log('❌ All Notion extraction approaches failed');
    return null;
}

// Extract page ID from various Notion URL formats
function extractNotionPageId(url) {
    // Handle different Notion URL formats:
    // https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278
    // https://notion.so/username/Page-Name-54c7c618172b4026a40ea94e584d0278

    const patterns = [
        /([a-f0-9]{32})/i,  // 32 character hex string
        /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i,  // UUID format
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
            let id = match[1];
            // Convert to UUID format if needed
            if (id.length === 32) {
                id = `${id.slice(0, 8)}-${id.slice(8, 12)}-${id.slice(12, 16)}-${id.slice(16, 20)}-${id.slice(20)}`;
            }
            return id;
        }
    }

    return null;
}

// Approach 1: Try with official Notion API (requires API key)
async function extractWithNotionAPI(pageId) {
    if (!process.env.NOTION_API_KEY) {
        throw new Error('NOTION_API_KEY not found in environment');
    }

    const notion = new Client({
        auth: process.env.NOTION_API_KEY,
    });

    const n2m = new NotionToMarkdown({ notionClient: notion });

    const mdblocks = await n2m.pageToMarkdown(pageId);
    const mdString = n2m.toMarkdownString(mdblocks);

    return mdString.parent;
}

// Approach 2: Try unofficial method for public pages
async function extractWithUnofficial(url, pageId) {
    // This would use unofficial Notion API or scraping methods
    // For now, let's try a simple fetch to the public API endpoint

    const publicApiUrl = `https://notion-api.splitbee.io/v1/page/${pageId}`;

    try {
        const response = await axios.get(publicApiUrl, {
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; NotionExtractor/1.0)',
            }
        });

        if (response.data && typeof response.data === 'object') {
            // Parse the Notion data structure to extract meaningful content
            const parsedContent = parseNotionContent(response.data);
            if (parsedContent && parsedContent.length > 100) {
                return parsedContent;
            }

            // Fallback to JSON if parsing fails
            return JSON.stringify(response.data, null, 2);
        }

        throw new Error('No valid data received');
    } catch (error) {
        throw new Error(`Unofficial API failed: ${error.message}`);
    }
}

// Approach 3: Try public Notion API endpoint
async function extractWithPublicAPI(pageId) {
    // Try the public Notion endpoint that sometimes works for public pages
    const endpoints = [
        `https://www.notion.so/api/v3/loadPageChunk`,
        `https://www.notion.so/api/v3/getRecordValues`,
    ];

    for (const endpoint of endpoints) {
        try {
            const response = await axios.post(endpoint, {
                pageId: pageId,
                limit: 100,
                cursor: { stack: [] },
                chunkNumber: 0,
                verticalColumns: false
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (compatible; NotionExtractor/1.0)',
                },
                timeout: 10000,
            });

            if (response.data && response.data.recordMap) {
                // Extract text content from the record map
                const blocks = response.data.recordMap.block || {};
                let content = '';

                Object.values(blocks).forEach(block => {
                    if (block.value && block.value.properties && block.value.properties.title) {
                        const title = block.value.properties.title[0];
                        if (typeof title === 'string') {
                            content += title + '\n';
                        } else if (Array.isArray(title)) {
                            content += title.join('') + '\n';
                        }
                    }
                });

                if (content.trim().length > 0) {
                    return content;
                }
            }
        } catch (error) {
            // Continue to next endpoint
            continue;
        }
    }

    throw new Error('Public API endpoints failed');
}

// Parse Notion content structure to extract readable text
function parseNotionContent(data) {
    let content = '';

    try {
        // The data structure varies, but typically contains blocks or records
        if (data.blocks) {
            // Handle blocks structure
            Object.values(data.blocks).forEach(block => {
                if (block && block.value && block.value.properties) {
                    const props = block.value.properties;
                    if (props.title) {
                        const title = extractTextFromProperty(props.title);
                        if (title) content += title + '\n';
                    }
                }
            });
        } else if (data.recordMap && data.recordMap.block) {
            // Handle recordMap structure
            Object.values(data.recordMap.block).forEach(block => {
                if (block && block.value && block.value.properties) {
                    const props = block.value.properties;
                    if (props.title) {
                        const title = extractTextFromProperty(props.title);
                        if (title) content += title + '\n';
                    }
                }
            });
        } else {
            // Try to extract any text content from the structure
            content = extractTextRecursively(data);
        }

        return content.trim();
    } catch (error) {
        console.log('Error parsing Notion content:', error.message);
        return '';
    }
}

// Extract text from Notion property format
function extractTextFromProperty(property) {
    if (!property || !Array.isArray(property)) return '';

    let text = '';
    property.forEach(item => {
        if (typeof item === 'string') {
            text += item;
        } else if (Array.isArray(item) && item.length > 0) {
            text += item[0]; // First element is usually the text
        }
    });

    return text.trim();
}

// Recursively extract text from any object structure
function extractTextRecursively(obj, depth = 0) {
    if (depth > 5) return ''; // Prevent infinite recursion

    let text = '';

    if (typeof obj === 'string') {
        return obj + ' ';
    } else if (Array.isArray(obj)) {
        obj.forEach(item => {
            text += extractTextRecursively(item, depth + 1);
        });
    } else if (obj && typeof obj === 'object') {
        Object.values(obj).forEach(value => {
            text += extractTextRecursively(value, depth + 1);
        });
    }

    return text;
}

async function testNotionExtraction() {
    console.log('🧪 Testing Notion Page Content Extraction...\n');

    const notionUrl = 'https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278';

    if (!process.env.TAVILY_API_KEY) {
        console.error('❌ TAVILY_API_KEY not found in environment variables');
        console.log('Please set TAVILY_API_KEY in your .env file to test this functionality');
        return;
    }

    try {
        console.log(`📄 Extracting content from: ${notionUrl}`);

        const tavilyClient = tavily({
            apiKey: process.env.TAVILY_API_KEY,
        });

        const response = await tavilyClient.extract([notionUrl], {
            maxResults: 1,
        });

        console.log('📊 Raw response:', JSON.stringify(response, null, 2));

        if (!response.results[0]) {
            console.error('❌ No content extracted from the Notion page via Tavily');
            console.log('🔍 Tavily failed, trying alternative extraction method...');

            // Try alternative extraction method
            const alternativeContent = await testDirectHttpExtraction(notionUrl);
            if (alternativeContent) {
                console.log('✅ Alternative extraction method worked!');
                return alternativeContent;
            }

            // Try the REAL Notion extraction implementation
            const realNotionContent = await testRealNotionExtraction(notionUrl);
            if (realNotionContent) {
                console.log('✅ Real Notion extraction worked!');
                return realNotionContent;
            } else {
                console.log('❌ All extraction methods failed');
                console.log('🔍 This could be due to:');
                console.log('  - Notion page is private or requires authentication');
                console.log('  - Notion is blocking automated access');
                console.log('  - The page uses heavy dynamic loading');
                console.log('  - Temporary network or API issues');
                return;
            }
        }

        const extractedResult = response.results[0];

        console.log('✅ Content extraction successful!');
        console.log(`📊 Content length: ${extractedResult.rawContent?.length || 0} characters`);

        if (extractedResult.rawContent) {
            // Show first 500 characters of extracted content
            console.log('\n📝 Sample extracted content:');
            console.log('='.repeat(50));
            console.log(extractedResult.rawContent.substring(0, 500) + '...');
            console.log('='.repeat(50));

            // Look for potential tool names/items
            const lines = extractedResult.rawContent.split('\n');
            const potentialTools = lines
                .filter(line => line.trim().length > 0)
                .filter(line => !line.startsWith('#'))
                .filter(line => line.length < 100) // Likely tool names are shorter
                .slice(0, 10); // Show first 10 potential tools

            if (potentialTools.length > 0) {
                console.log('\n🔧 Potential tools/items found:');
                potentialTools.forEach((tool, index) => {
                    console.log(`${index + 1}. ${tool.trim()}`);
                });
            }
        }

        console.log('\n🎉 Test completed successfully!');
        console.log('The items-generator should be able to extract items from this Notion page.');

    } catch (error) {
        console.error('❌ Error during extraction:', error.message);

        if (error.message.includes('401') || error.message.includes('unauthorized')) {
            console.log('💡 This might be due to Notion page permissions or API key issues');
        } else if (error.message.includes('403') || error.message.includes('forbidden')) {
            console.log('💡 The Notion page might be private or have restricted access');
        } else {
            console.log('💡 This could be a temporary network issue or API limitation');
        }
    }
}

// Test URL extraction as well
function testUrlExtraction() {
    console.log('\n🔗 Testing URL extraction from prompt...');

    const testPrompt = "Extract PM and time tracking software from https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278";

    // Simple regex test (similar to what the service does as fallback)
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const extractedUrls = testPrompt.match(urlRegex);

    if (extractedUrls && extractedUrls.length > 0) {
        console.log('✅ URL extraction successful!');
        console.log('📎 Extracted URLs:');
        extractedUrls.forEach((url, index) => {
            console.log(`${index + 1}. ${url}`);
        });
    } else {
        console.log('❌ No URLs found in the prompt');
    }
}

// Alternative extraction method using direct HTTP request
async function testDirectHttpExtraction(url) {
    console.log('\n🔄 Trying direct HTTP extraction...');

    try {
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            },
            timeout: 10000,
        });

        const $ = cheerio.load(response.data);

        // Remove script and style elements
        $('script, style').remove();

        // Extract text content
        const textContent = $('body').text().trim();

        if (textContent && textContent.length > 100) {
            console.log('✅ Direct HTTP extraction successful!');
            console.log(`📊 Content length: ${textContent.length} characters`);

            // Look for potential tool names
            const lines = textContent.split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0 && line.length < 100)
                .slice(0, 10);

            if (lines.length > 0) {
                console.log('\n🔧 Sample content lines:');
                lines.forEach((line, index) => {
                    console.log(`${index + 1}. ${line}`);
                });
            }

            return textContent;
        } else {
            console.log('❌ Direct HTTP extraction failed - no meaningful content');
            return null;
        }

    } catch (error) {
        console.log(`❌ Direct HTTP extraction failed: ${error.message}`);
        return null;
    }
}

// Test with a simple public page to verify Tavily works
async function testSimpleExtraction() {
    console.log('\n🧪 Testing with a simple public page...\n');

    const testUrl = 'https://github.com/microsoft/vscode';

    try {
        const tavilyClient = tavily({
            apiKey: process.env.TAVILY_API_KEY,
        });

        console.log(`📄 Extracting content from: ${testUrl}`);
        const response = await tavilyClient.extract([testUrl], {
            maxResults: 1,
        });

        if (response.results[0] && response.results[0].rawContent) {
            console.log('✅ Simple extraction successful!');
            console.log(`📊 Content length: ${response.results[0].rawContent.length} characters`);
            console.log('🔧 Tavily extraction is working properly');
        } else {
            console.log('❌ Simple extraction failed - Tavily might have issues');
        }
    } catch (error) {
        console.error('❌ Error during simple extraction:', error.message);
    }
}

// Run the tests
async function runTests() {
    console.log('🚀 Starting Notion Integration Tests\n');
    await testNotionExtraction();
}


runTests()
    .catch(console.error);
