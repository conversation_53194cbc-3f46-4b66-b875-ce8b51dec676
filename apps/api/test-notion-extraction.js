/**
 * Test script to verify Notion page content extraction
 * This tests if the items-generator can extract content from the Notion page
 */

const { tavily } = require('@tavily/core');

async function testNotionExtraction() {
    console.log('🧪 Testing Notion Page Content Extraction...\n');
    
    const notionUrl = 'https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278';
    
    if (!process.env.TAVILY_API_KEY) {
        console.error('❌ TAVILY_API_KEY not found in environment variables');
        console.log('Please set TAVILY_API_KEY in your .env file to test this functionality');
        return;
    }
    
    try {
        console.log(`📄 Extracting content from: ${notionUrl}`);
        
        const tavilyClient = tavily({
            apiKey: process.env.TAVILY_API_KEY,
        });
        
        const response = await tavilyClient.extract([notionUrl], {
            maxResults: 1,
        });
        
        if (!response.results[0]) {
            console.error('❌ No content extracted from the Notion page');
            return;
        }
        
        const extractedResult = response.results[0];
        
        console.log('✅ Content extraction successful!');
        console.log(`📊 Content length: ${extractedResult.rawContent?.length || 0} characters`);
        
        if (extractedResult.rawContent) {
            // Show first 500 characters of extracted content
            console.log('\n📝 Sample extracted content:');
            console.log('=' .repeat(50));
            console.log(extractedResult.rawContent.substring(0, 500) + '...');
            console.log('=' .repeat(50));
            
            // Look for potential tool names/items
            const lines = extractedResult.rawContent.split('\n');
            const potentialTools = lines
                .filter(line => line.trim().length > 0)
                .filter(line => !line.startsWith('#'))
                .filter(line => line.length < 100) // Likely tool names are shorter
                .slice(0, 10); // Show first 10 potential tools
                
            if (potentialTools.length > 0) {
                console.log('\n🔧 Potential tools/items found:');
                potentialTools.forEach((tool, index) => {
                    console.log(`${index + 1}. ${tool.trim()}`);
                });
            }
        }
        
        console.log('\n🎉 Test completed successfully!');
        console.log('The items-generator should be able to extract items from this Notion page.');
        
    } catch (error) {
        console.error('❌ Error during extraction:', error.message);
        
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
            console.log('💡 This might be due to Notion page permissions or API key issues');
        } else if (error.message.includes('403') || error.message.includes('forbidden')) {
            console.log('💡 The Notion page might be private or have restricted access');
        } else {
            console.log('💡 This could be a temporary network issue or API limitation');
        }
    }
}

// Test URL extraction as well
function testUrlExtraction() {
    console.log('\n🔗 Testing URL extraction from prompt...');
    
    const testPrompt = "Extract PM and time tracking software from https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278";
    
    // Simple regex test (similar to what the service does as fallback)
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const extractedUrls = testPrompt.match(urlRegex);
    
    if (extractedUrls && extractedUrls.length > 0) {
        console.log('✅ URL extraction successful!');
        console.log('📎 Extracted URLs:');
        extractedUrls.forEach((url, index) => {
            console.log(`${index + 1}. ${url}`);
        });
    } else {
        console.log('❌ No URLs found in the prompt');
    }
}

// Run the tests
async function runTests() {
    console.log('🚀 Starting Notion Integration Tests\n');
    
    testUrlExtraction();
    await testNotionExtraction();
    
    console.log('\n📋 Summary:');
    console.log('- URL extraction: Tests if URLs are properly extracted from prompts');
    console.log('- Content extraction: Tests if Notion page content can be retrieved');
    console.log('- The actual items-generator will use AI to parse the content and extract structured items');
}

runTests().catch(console.error);
